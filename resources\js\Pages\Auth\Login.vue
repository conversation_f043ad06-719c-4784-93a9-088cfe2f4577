<script setup>
import Checkbox from '@/Components/Checkbox.vue';
import GuestLayout from '@/Layouts/GuestLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import { Head, Link, useForm } from '@inertiajs/vue3';

defineProps({
    canResetPassword: {
        type: Boolean,
    },
    status: {
        type: String,
    },
});

const form = useForm({
    email: '',
    password: '',
    remember: false,
});

const submit = () => {
    form.post(route('login'), {
        onFinish: () => form.reset('password'),
    });
};
</script>

<template>
    <GuestLayout>

        <Head title="Log in" />

        <div v-if="status" class="mb-4 font-medium text-sm text-green-600">
            {{ status }}
        </div>

        <form @submit.prevent="submit">
            <div>
                <InputLabel for="email" value="Email" />

                <TextInput id="email" type="email" class="mt-1 block w-full" v-model="form.email" required autofocus
                    autocomplete="username" />

                <InputError class="mt-2" :message="form.errors.email" />
            </div>

            <div class="mt-4">
                <InputLabel for="password" value="Password" />

                <TextInput id="password" type="password" class="mt-1 block w-full" v-model="form.password" required
                    autocomplete="current-password" />

                <InputError class="mt-2" :message="form.errors.password" />
            </div>

            <div class="block mt-4">
                <label class="flex items-center">
                    <Checkbox name="remember" v-model:checked="form.remember" />
                    <span class="ms-2 text-sm text-gray-600 dark:text-gray-400">Remember me</span>
                </label>
            </div>

            <div class="flex items-center justify-end mt-4">
                <Link v-if="canResetPassword" :href="route('password.request')"
                    class="underline text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800">
                Forgot your password?
                </Link>

                <PrimaryButton class="ms-4" :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                    Log in
                </PrimaryButton>
            </div>
        </form>

        <div class="relative flex py-5 items-center">
            <div class="flex-grow border-t border-gray-400"></div>
            <span class="flex-shrink mx-4 text-gray-400">Or</span>
            <div class="flex-grow border-t border-gray-400"></div>
        </div>

        <div class="flex flex-col items-center mb-4">
            <a href="/auth/google/redirect"
                class="inline-flex items-center px-4 py-2 bg-gray-900 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition ease-in-out duration-150">
                <svg class="w-4 h-4 mr-2" viewBox="0 0 48 48">
                    <path fill="#EA4335"
                        d="M24 9.5c3.34 0 6.33 1.16 8.69 3.07l6.5-6.5C34.74 2.17 29.6 0 24 0 14.96 0 7.29 5.86 3.88 14.22l7.54 5.86C13.13 13.57 18.15 9.5 24 9.5z" />
                    <path fill="#34A853"
                        d="M46.1 24.5c0-1.7-.14-3.36-.41-4.96H24v9.41h12.44c-.54 2.92-2.17 5.4-4.6 7.05l7.26 5.64C43.89 37.58 46.1 31.55 46.1 24.5z" />
                    <path fill="#4A90E2"
                        d="M10.58 28.64a14.55 14.55 0 010-9.29L3.03 13.5C1.08 17.26 0 21.45 0 26s1.08 8.74 3.03 12.5l7.55-5.86z" />
                    <path fill="#FBBC05"
                        d="M24 48c6.6 0 12.14-2.17 16.19-5.86l-7.26-5.64c-2.04 1.37-4.63 2.15-8.93 2.15-5.85 0-10.87-4.07-12.58-9.57l-7.54 5.86C7.29 42.14 14.96 48 24 48z" />
                </svg>
                Continue with Google
            </a>
        </div>

    </GuestLayout>
</template>
