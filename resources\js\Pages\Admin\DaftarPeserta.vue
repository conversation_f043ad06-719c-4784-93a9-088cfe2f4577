<script setup>
import SearchForm from "@/Components/SearchForm.vue";
import FilterDropdown from "@/Components/FilterDropdown.vue";
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";
import { Head, router } from "@inertiajs/vue3";
import axios from "axios";
import {
    Chart,
    Title,
    Tooltip,
    Legend,
    BarElement,
    CategoryScale,
    LinearScale,
} from "chart.js";
import { computed, ref } from "vue";
import JSZip from 'jszip';
import { saveAs } from 'file-saver';

// Inisialisasi Chart.js
Chart.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

// Definisi props dari Inertia
const props = defineProps({
    peserta: {
        type: Array
    },
    wave: {
        type: Object
    }
});

// State untuk modal detail
const isDetailModalOpen = ref(false);
const selectedPeserta = ref(null);
const isLoadingDetail = ref(false);
const isZipping = ref(false);

// --- FUNGSI-FUNGSI ---

/**
 * Menampilkan modal detail dan mengambil data lengkap peserta dari server.
 * @param {number} pesertaId - ID dari peserta yang akan ditampilkan.
 */
const showDetail = (pesertaId) => {
    isLoadingDetail.value = true;
    isDetailModalOpen.value = true;
    selectedPeserta.value = null; // Reset data sebelumnya

    // Panggil API endpoint untuk mendapatkan detail peserta
    axios.get(`/admin/daftar-peserta/${pesertaId}`)
        .then(response => {
            selectedPeserta.value = response.data;
        })
        .catch(error => {
            console.error("Gagal mengambil data detail:", error);
            alert('Gagal memuat data detail peserta.');
            isDetailModalOpen.value = false; // Tutup modal jika terjadi error
        })
        .finally(() => {
            isLoadingDetail.value = false;
        });
};

/**
 * Menutup modal detail.
 */
const closeDetailModal = () => {
    isDetailModalOpen.value = false;
    selectedPeserta.value = null;
};

const downloadAllFilesAsZip = async () => {
    if (!selectedPeserta.value || !selectedPeserta.value.get_form) return;

    isZipping.value = true;
    const zip = new JSZip();
    const form = selectedPeserta.value.get_form;
    const namaPeserta = selectedPeserta.value.name.replace(/\s+/g, '_'); // Ganti spasi dengan underscore untuk nama file

    // Daftar file yang akan diunduh
    const fileMetas = [
        { url: form.ktp_url, name: 'KTP' },
        { url: form.ijazah_url, name: 'Ijazah' },
        { url: form.kk_url, name: 'Kartu_Keluarga' },
        { url: form.foto_url, name: 'Foto' }
    ];

    const promises = fileMetas
        .filter(file => file.url) // Hanya proses file yang memiliki URL
        .map(fileMeta => {
            // Mengambil setiap file sebagai blob
            return fetch(fileMeta.url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`Gagal mengunduh ${fileMeta.name} dari ${fileMeta.url}`);
                    }
                    return response.blob();
                })
                .then(blob => {
                    // Menentukan ekstensi file dari URL
                    const urlParts = fileMeta.url.split('?')[0].split('.');
                    const extension = urlParts[urlParts.length - 1] || 'jpg';
                    // Menambahkan file ke dalam objek zip
                    zip.file(`${fileMeta.name}_${namaPeserta}.${extension}`, blob);
                })
                .catch(error => {
                    console.error(error);
                    // Anda bisa menambahkan notifikasi error kepada pengguna di sini
                });
        });

    try {
        // Menunggu semua proses fetch dan penambahan ke zip selesai
        await Promise.all(promises);

        if (Object.keys(zip.files).length > 0) {
            // Menghasilkan file zip dalam bentuk blob
            const content = await zip.generateAsync({ type: 'blob' });
            // Memicu unduhan menggunakan FileSaver.js
            saveAs(content, `Berkas_${namaPeserta}.zip`);
        } else {
            alert('Tidak ada berkas yang bisa diunduh.');
        }

    } catch (error) {
        console.error("Gagal membuat file zip:", error);
        alert('Terjadi kesalahan saat membuat file zip.');
    } finally {
        isZipping.value = false; // Selesaikan state loading
    }
};


/**
 * Menghapus data peserta berdasarkan ID.
 * @param {number} id - ID dari peserta yang akan dihapus.
 */
const deleteData = (id) => {
    if (confirm("Apakah Anda yakin ingin menghapus data peserta ini?")) {
        router.delete(`/admin/daftar-peserta/${id}`, {
            preserveScroll: true,
            onSuccess: () => {
                // Mungkin bisa ditambahkan notifikasi sukses
            },
            onError: (errors) => {
                alert('Gagal menghapus data. Pastikan tidak ada data terkait lain.');
                console.error('Error:', errors);
            }
        });
    }
};

// --- Logika Filter dan Pencarian ---
const searchFilter = ref('');
const tahunAkademikFilter = ref([]);
const gelombangFilter = ref([]);
const prodiFilter = ref([]);
const kelasFilter = ref([]);

const filterItems = computed(() => {
    let peserta = props.peserta;
    if (tahunAkademikFilter.value.length) {
        peserta = peserta.filter(p => tahunAkademikFilter.value.includes(p.get_form?.wave?.tahun_akademik));
    }
    if (gelombangFilter.value.length) {
        peserta = peserta.filter(p => gelombangFilter.value.includes(p.get_form?.wave?.gelombang));
    }
    if (prodiFilter.value.length) {
        peserta = peserta.filter(p => prodiFilter.value.includes(p.get_form?.prodi?.nama_prodi));
    }
    if (kelasFilter.value.length) {
        peserta = peserta.filter(p => kelasFilter.value.includes(p.get_form?.kelas?.nama_kelas));
    }
    if (searchFilter.value !== '') {
        peserta = peserta.filter(p => p.name.toLowerCase().includes(searchFilter.value.toLowerCase()));
    }
    return peserta;
});

const handleSearch = (search) => {
    searchFilter.value = search;
};

const handleCheckbox = (filters) => {
    tahunAkademikFilter.value = filters.tahunAkademik;
    prodiFilter.value = filters.prodi;
    kelasFilter.value = filters.kelas;
    gelombangFilter.value = filters.wave;
};

/**
 * Mengekspor data yang telah difilter ke dalam file Excel.
 */
const exportData = () => {
    axios.post('/admin/export-peserta', {
        filteredData: JSON.parse(JSON.stringify(filterItems.value)),
        nim: 'test NIM'
    }, {
        responseType: 'blob'
    })
        .then(response => {
            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement('a');
            link.href = url;
            const sekarang = new Date();
            const hari = String(sekarang.getDate()).padStart(2, '0');
            const bulan = String(sekarang.getMonth() + 1).padStart(2, '0');
            const tahun = sekarang.getFullYear();
            const jam = String(sekarang.getHours()).padStart(2, '0');
            const menit = String(sekarang.getMinutes()).padStart(2, '0');
            const detik = String(sekarang.getSeconds()).padStart(2, '0');
            const formatted = `${hari}_${bulan}_${tahun}_${jam}_${menit}_${detik}`;
            link.setAttribute('download', `DATA_PESERTA_${formatted}.xlsx`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        })
        .catch(error => {
            console.error('Error downloading the file:', error);
        });
};
</script>

<template>

    <Head title="Daftar Peserta" />
    <AuthenticatedLayout>
        <div>
            <div class="max-w-7xl mx-auto">
                <div class="shadow-md sm:shadow-lg p-4 sm:p-8 bg-white">
                    <div
                        class="flex flex-column sm:flex-row flex-wrap space-y-4 sm:space-y-0 items-center justify-between gap-3 pb-4">
                        <header class="flex flex-row items-center">
                            <h2 class="text-lg font-medium text-gray-900">Daftar Peserta</h2>
                            <SearchForm @search="handleSearch" />
                        </header>
                        <div class="flex flex-columm justify-between">
                            <FilterDropdown :items="peserta" @filter="handleCheckbox" />
                            <button @click="exportData"
                                class="w-full flex items-center justify-center py-2 px-4 text-sm font-semibold text-white rounded-md hover:bg-red-700 bg-red-400">
                                <i class="fas fa-print mr-1"></i>Export
                            </button>
                        </div>
                    </div>

                    <div class="relative overflow-x-auto">
                        <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3">Nama lengkap</th>
                                    <th scope="col" class="px-6 py-3">Tahun Akademik</th>
                                    <th scope="col" class="px-6 py-3">Prodi Pilihan</th>
                                    <th scope="col" class="px-6 py-3">Email</th>
                                    <th scope="col" class="px-6 py-3">Action</th>
                                </tr>
                            </thead>
                            <tbody class="uppercase">
                                <tr class="bg-white border-b hover:bg-gray-50" v-for="p in filterItems" :key="p.id">
                                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap">{{
                                        p.name }}</th>
                                    <td class="px-6 py-4 capitalize">{{ p.get_form?.wave?.tahun_akademik ?? '(Kosong)'
                                        }}</td>
                                    <td class="px-6 py-4">{{ p.get_form?.prodi?.nama_prodi ?? '(Kosong)' }}</td>
                                    <td class="px-6 py-4 lowercase">{{ p.email }}</td>
                                    <td class="px-6 py-4 flex gap-3">
                                        <button @click="showDetail(p.id)" class="text-blue-600 hover:text-blue-900"
                                            title="Lihat Detail">
                                            <i class="fa-solid fa-eye"></i>
                                        </button>
                                        <button @click="deleteData(p.id)" class="text-red-600 hover:text-red-900"
                                            title="Hapus Data">
                                            <i class="fa-solid fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div v-if="isDetailModalOpen" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60"
            @click.self="closeDetailModal">
            <div class="bg-white p-6 sm:p-8 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                <!-- Header Modal -->
                <div class="flex justify-between items-center pb-4 border-b">
                    <h2 class="text-2xl font-bold text-gray-800">Detail Peserta</h2>
                    <button @click="closeDetailModal"
                        class="text-gray-500 hover:text-gray-800 text-2xl">&times;</button>
                </div>

                <!-- Body Modal -->
                <div class="mt-6">
                    <!-- Loading Spinner -->
                    <div v-if="isLoadingDetail" class="text-center p-10">
                        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
                        <p class="mt-4 text-gray-600">Memuat data...</p>
                    </div>

                    <!-- Detail Content -->
                    <div v-if="selectedPeserta && !isLoadingDetail">
                        <!-- Data Diri -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-700 border-b pb-2 mb-3">Data Diri</h3>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-3 text-sm">
                                <div><strong class="text-gray-500">Nama Lengkap:</strong>
                                    <p class="text-gray-800">{{ selectedPeserta.name }}</p>
                                </div>
                                <div><strong class="text-gray-500">Email:</strong>
                                    <p class="text-gray-800 lowercase">{{ selectedPeserta.email }}</p>
                                </div>
                                <div><strong class="text-gray-500">NIK:</strong>
                                    <p class="text-gray-800">{{ selectedPeserta.get_form?.national_id ?? 'Tidak diisi'
                                        }}</p>
                                </div>
                                <div><strong class="text-gray-500">No. WhatsApp:</strong>
                                    <p class="text-gray-800">{{ selectedPeserta.get_form?.phone_number ?? 'Tidak diisi'
                                        }}</p>
                                </div>
                                <div><strong class="text-gray-500">Nama Ibu:</strong>
                                    <p class="text-gray-800">{{ selectedPeserta.get_form?.mother_name ?? 'Tidak diisi'
                                        }}</p>
                                </div>
                                <div class="col-span-2"><strong class="text-gray-500">Alamat:</strong>
                                    <p class="text-gray-800">{{ selectedPeserta.get_form?.address ?? 'Tidak diisi' }}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Data Akademik -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-700 border-b pb-2 mb-3">Data Akademik Pilihan
                            </h3>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-3 text-sm">
                                <div><strong class="text-gray-500">Tahun Akademik:</strong>
                                    <p class="text-gray-800">{{ selectedPeserta.get_form?.wave?.tahun_akademik ?? '-' }}
                                    </p>
                                </div>
                                <div><strong class="text-gray-500">Gelombang:</strong>
                                    <p class="text-gray-800">{{ selectedPeserta.get_form?.wave?.gelombang ?? '-' }}</p>
                                </div>
                                <div><strong class="text-gray-500">Program Studi:</strong>
                                    <p class="text-gray-800">{{ selectedPeserta.get_form?.prodi?.nama_prodi ?? '-' }}
                                    </p>
                                </div>
                                <div><strong class="text-gray-500">Kelas:</strong>
                                    <p class="text-gray-800">{{ selectedPeserta.get_form?.kelas?.nama_kelas ?? '-' }}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Berkas Terlampir -->
                        <div>
                            <h3 class="text-lg font-semibold text-gray-700 border-b pb-2 mb-3">Berkas Terlampir</h3>
                            <div
                                v-if="selectedPeserta.get_form?.ktp_url || selectedPeserta.get_form?.ijazah_url || selectedPeserta.get_form?.foto_url || selectedPeserta.get_form?.kk_url">

                                <!-- [MODIFIKASI] Tombol Unduh Semua sebagai ZIP -->
                                <button @click="downloadAllFilesAsZip" :disabled="isZipping"
                                    class="flex items-center justify-center gap-3 px-4 py-2 mb-4 text-sm font-medium text-white bg-purple-600 rounded-md hover:bg-purple-700 transition-colors w-full sm:w-auto disabled:bg-purple-300 disabled:cursor-not-allowed">
                                    <i v-if="!isZipping" class="fa-solid fa-file-zipper"></i>
                                    <div v-else class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                    <span>{{ isZipping ? 'Sedang memproses...' : 'Unduh Semua Berkas (.zip)' }}</span>
                                </button>
                            </div>
                            <div v-else class="text-sm text-gray-500 italic mt-3">
                                Tidak ada berkas yang dilampirkan oleh peserta.
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Footer Modal -->
                <div class="flex justify-end pt-6 border-t mt-6">
                    <button @click="closeDetailModal"
                        class="px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
                        Tutup
                    </button>
                </div>
            </div>
        </div>
    </AuthenticatedLayout>
</template>
